package middlewares

import (
	"context"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

type Claims struct {
	Sub string `json:"sub"`
	jwt.RegisteredClaims
}

func GqlJwtAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Check if this is an introspection query (already set by ApiKeyAuth middleware)
		if ctx.Request.Context().Value("isIntrospection") != nil {
			ctx.Next()
			return
		}

		authHeader := ctx.GetHeader("Authorization")
		if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
			ctx.Next()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")

		// Validate JWT token properly using the JWT config
		claims, err := utils.ValidateJWTToken(tokenString, global.GVA_CONFIG.JWT)
		if err != nil {
			ctx.Next()
			return
		}

		wrappedCtx := context.WithValue(ctx.Request.Context(), "userId", claims.UserID.String())
		ctx.Request = ctx.Request.WithContext(wrappedCtx)
		ctx.Next()
	}
}
